package com.alibaba.copilot.enabler.service.subscription.manager.t2g;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionPlanDTO;
import com.alibaba.copilot.enabler.client.subscription.service.Text2GoSubscriptionService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.subscription.repository.SubscriptionPlanRepositoryImpl;
import com.alibaba.copilot.enabler.service.subscription.manager.AbstractSubscriptionManager;
import com.alibaba.copilot.enabler.service.subscription.manager.BizSubscriptionContext;
import com.alibaba.copilot.enabler.service.subscription.service.Text2GoSubscriptionServiceImpl;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alipay.global.api.AlipayClient;
import com.alipay.global.api.DefaultAlipayClient;
import com.alipay.global.api.exception.AlipayApiException;
import com.alipay.global.api.model.ams.*;
import com.alipay.global.api.model.ams.PaymentMethod;
import com.alipay.global.api.request.ams.subscription.AlipaySubscriptionCreateRequest;
import com.alipay.global.api.response.ams.subscription.AlipaySubscriptionCreateResponse;
import com.google.common.collect.Lists;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.param.CouponListParams;
import com.stripe.param.PriceListParams;

import com.taobao.mtop.api.domain.wrap.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.compress.archivers.sevenz.CLI;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

// 以下是Antom支付相关的依赖，需要在pom.xml中添加
// import com.alipay.api.AlipayClient;
// import com.alipay.api.DefaultAlipayClient;

/**
 * @see com.alibaba.copilot.enabler.service.subscription.manager.t2g.StripeSubManagerForT2g
 * 大部分业务逻辑保持不变，同时，优惠码、价格信息，依然从 stripe 获取，这样能够有效保持一致性。
 * 
 */
@Controller
@Slf4j
public class AntomSubManagerForT2g extends AbstractSubscriptionManager {

    @Resource
    private StripeSubManagerForT2g stripeSubManagerForT2g;
    @Autowired
    private SubscriptionPlanRepositoryImpl subscriptionPlanRepository;
    private static AlipayClient CLIENT = null;
    @Autowired
    private Text2GoSubscriptionService text2GoSubscriptionService;

    /**
     * Antom支付端点常量
     */
    private static class EndPointConstants {
        public static final String SG = "https://sg-openapi.antfinance.com";
    }

    @PostConstruct
    public void init() {
        CLIENT = new DefaultAlipayClient(
                EndPointConstants.SG, SwitchConfig.merchantPrivateKey, SwitchConfig.antomPublicKey, SwitchConfig.antomClientId);

    }

    @Override
    protected SingleResult<SubscribePlanResultDTO> doSubscribe(BizSubscriptionContext context) {
        try {
            // TODO: 实现Antom支付的订阅逻辑，参考StripeSubManagerForT2g
            log.info("AntomSubManagerForT2g.doSubscribe, userId={}, input={}", 
                context.getSubscribePlanDTO().getUserId(), 
                JSONObject.toJSONString(context.getSubscribePlanDTO()));
            return SingleResult.buildSuccess(createAntomOrder(context.getSubscribePlanDTO()));
        } catch (Exception e) {
            log.error("AntomSubManagerForT2g error ", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    private Price fetchPriceFromStripe(String lookupKey) {
        try {
            PriceListParams priceParams = PriceListParams.builder()
                    .addLookupKey(lookupKey)
                    .build();
            PriceCollection prices = Price.list(priceParams);
            if (prices.getData().isEmpty()) {
                throw new RuntimeException("No price found for lookup key: " + lookupKey);
            }
            return prices.getData().get(0);
        } catch (StripeException e) {
            log.error("Failed to fetch price from Stripe", e);
            throw new RuntimeException("Failed to fetch price from Stripe: " + e.getMessage());
        }
    }
    
    private SubscribePlanResultDTO createAntomOrder(SubscribePlanDTO subscribePlanDTO) throws StripeException {

        // 1. 获取价格信息
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Long planId = subscribePlanDTO.getPlanId();
        String key = SwitchConfig.t2gPlanId2LookupKey.get(planId.toString());
        Price price = fetchPriceFromStripe(key);
        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(planId, false);
        // 2.1 创建新订单
        Long subscriptionOrder = stripeSubManagerForT2g.createSubscribeOrder(subscribePlanDTO, planId, subscriptionPlan);
        AlipaySubscriptionCreateRequest alipaySubscriptionCreateRequest = new AlipaySubscriptionCreateRequest();
        // todo huangmeng 切换订阅



        // 3. todo huangmeng 返回结果

        return new SubscribePlanResultDTO();
    }

    private void createNewOrder(SubscribePlanDTO subscribePlanDTO, Long subOrderId, Price price) {
        AlipaySubscriptionCreateRequest alipaySubscriptionCreateRequest = new AlipaySubscriptionCreateRequest();

        String subscriptionRequestId = String.valueOf(subOrderId);
        alipaySubscriptionCreateRequest.setSubscriptionRequestId(subscriptionRequestId);
        alipaySubscriptionCreateRequest.setSubscriptionDescription("Subscription Description");

        // set subscription start time and end time. you might want to consider time zones
        // If the start time is earlier than the authorization time, the subscription is successful.
        // If the start time is later than the authorization time, the payment is made after the successful authorization, which is the pre-sale
        // For details, please refer to: <a href="https://docs.antom.com/ac/subscriptionpay/activation#uiqBb">Samples</a>
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
        alipaySubscriptionCreateRequest.setSubscriptionStartTime(ZonedDateTime.now().format(formatter));
        alipaySubscriptionCreateRequest.setSubscriptionEndTime(ZonedDateTime.now().plusYears(3).format(formatter));

        // set periodRule
        PeriodRule periodRule = PeriodRule.builder().periodCount(Integer.MAX_VALUE).periodType(price.getRecurring().getInterval()).build();
        alipaySubscriptionCreateRequest.setPeriodRule(periodRule);

        // set paymentMethod
        PaymentMethod paymentMethod = PaymentMethod.builder().paymentMethodType(WalletPaymentMethodType.GCASH.name()).build();
        alipaySubscriptionCreateRequest.setPaymentMethod(paymentMethod);

        // convert amount unit(in practice, amount should be calculated on your serverside)
        // For details, please refer to: <a href="https://docs.antom.com/ac/ref/cc">Usage rules of the Amount object</a>
        long amountMinorLong = price.getUnitAmount();
        // todo huangmeng 优惠码
        // 考虑优惠码信息
        try {
            applyCoupon(subscribePlanDTO.getDiscountCode(), alipaySubscriptionCreateRequest, subscribePlanDTO, price);
        } catch (StripeException e) {
            log.error("Failed to apply coupon: {}", e.getMessage());
            // 优惠码失败时继续处理，不应用优惠
        }

        Amount amount = Amount.builder().currency(price.getCurrency()).value(String.valueOf(amountMinorLong)).build();

        // set payment amount
        alipaySubscriptionCreateRequest.setPaymentAmount(amount);

        // set order info
        OrderInfo orderInfo = OrderInfo.builder().orderAmount(amount).build();
        alipaySubscriptionCreateRequest.setOrderInfo(orderInfo);

        // set settlement strategy
        // replace with your existing settlement currency
        SettlementStrategy settlementStrategy = SettlementStrategy.builder().settlementCurrency("USD").build();
        alipaySubscriptionCreateRequest.setSettlementStrategy(settlementStrategy);

        // set env info
        Env env = Env.builder().terminalType(TerminalType.WEB).build();
        alipaySubscriptionCreateRequest.setEnv(env);
        // todo huangmeng  replace with your subscription redirect url
        alipaySubscriptionCreateRequest.setSubscriptionRedirectUrl("http://localhost:8080/index.html?subscriptionRequestId=" + subscriptionRequestId);

        // do authorization consult
        AlipaySubscriptionCreateResponse alipaySubscriptionCreateResponse;
        try {
            long startTime = System.currentTimeMillis();
            log.info("subscription create request: " + JSON.toJSONString(alipaySubscriptionCreateRequest));
            alipaySubscriptionCreateResponse = CLIENT.execute(alipaySubscriptionCreateRequest);
            log.info("subscription create response: " + JSON.toJSONString(alipaySubscriptionCreateResponse));
        } catch (AlipayApiException e) {
            // exception handling
            log.info("AntomSubManagerForT2g_createNewOrder error {} ", JSON.toJSONString(new Object[]{subOrderId, price}));
        }
    }

    private void applyCoupon(String discountCode, AlipaySubscriptionCreateRequest request, SubscribePlanDTO subscribePlanDTO, Price price) throws StripeException {
        PromotionCode promotionCode = PromotionCode.retrieve(discountCode);
        Coupon coupon = promotionCode.getCoupon();
        Long priceBeforeDiscount = price.getUnitAmount();
        Long finalPrice = priceBeforeDiscount;
        if (coupon.getAmountOff() != null) {
            finalPrice -= coupon.getAmountOff();
        } else if (coupon.getPercentOff() != null) {
            long amountOff = BigDecimal.valueOf(priceBeforeDiscount).multiply(coupon.getPercentOff()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).longValue();
            finalPrice -= amountOff;
        }
        request.setPaymentAmount(Amount.builder().currency(price.getCurrency()).value(String.valueOf(price.getUnitAmount())).build());

    }

    public static void main(String[] args) throws StripeException {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Subscription subscription = Subscription.retrieve("sub_1RBvLaBUuFYymNc8dw2OU0fS");
        Long currentPrice = subscription.getItems().getData().get(0).getPrice().getUnitAmount();
        Long currentPeriodStart = subscription.getCurrentPeriodStart();
        Long currentPeriodEnd = subscription.getCurrentPeriodEnd();
        Long upgradeProration = calculateUpgradeProration(currentPrice, 23988L, currentPeriodStart, currentPeriodEnd, new ArrayList<>(), System.currentTimeMillis() / 1000);
        System.out.println(upgradeProration);
    }

    /**
     * 计算订阅升级的差价
     *
     * @param currentPrice 当前订阅价格（分）
     * @param newPrice 新订阅价格（分）
     * @param currentPeriodStart 当前订阅周期开始时间（秒时间戳）
     * @param currentPeriodEnd 当前订阅周期结束时间（秒时间戳）
     * @param discounts 折扣信息（可选）
     * @param prorationTime 按比例计算的时间点（秒时间戳）
     * @return 升级需要支付的差价（分）
     */
    public static Long calculateUpgradeProration(Long currentPrice, Long newPrice, Long currentPeriodStart,
                                         Long currentPeriodEnd,
                                         List<Map<String, Object>> discounts, Long prorationTime) {
        // 校验参数
        if (currentPrice == null || newPrice == null || currentPeriodStart == null || 
            currentPeriodEnd == null  || prorationTime == null) {
            throw new IllegalArgumentException("Required parameters cannot be null");
        }
        
        // 判断是否为升级（新价格大于旧价格）
        if (newPrice <= currentPrice) {
            throw new IllegalArgumentException("New price must be greater than current price for upgrade calculation " + currentPrice + " " + newPrice);
        }
        
        // 确保按照Stripe逻辑，取当前时间和订阅周期开始时间的较大值作为按比例计算的起点
        long prorationDate = Math.max(prorationTime, currentPeriodStart);
        
        // 如果已超出订阅周期，直接返回新价格
        if (prorationDate >= currentPeriodEnd) {
            return newPrice;
        }
        
        // 计算剩余周期的比例
        BigDecimal remainingTime = new BigDecimal(currentPeriodEnd - prorationDate);
        BigDecimal totalPeriodTime = new BigDecimal(currentPeriodEnd - currentPeriodStart);
        BigDecimal remainingRatio = remainingTime.divide(totalPeriodTime, 10, RoundingMode.HALF_UP);
        
        // 计算未使用部分的价值
        BigDecimal currentPriceBD = new BigDecimal(currentPrice);
        BigDecimal unusedAmount = currentPriceBD.multiply(remainingRatio).setScale(0, RoundingMode.HALF_UP);
        
        // 计算新订阅在剩余时间的价值
        BigDecimal newPriceBD = new BigDecimal(newPrice);
        BigDecimal newAmountForRemainingTime = newPriceBD.multiply(remainingRatio).setScale(0, RoundingMode.HALF_UP);
        
        // 计算需要支付的差价
        BigDecimal proratedDifference = newAmountForRemainingTime.subtract(unusedAmount);
        
        // 考虑折扣（如果有）
        if (discounts != null && !discounts.isEmpty()) {
            for (Map<String, Object> discount : discounts) {
                // 百分比折扣
                if (discount.containsKey("percentOff")) {
                    BigDecimal percentOff = new BigDecimal(discount.get("percentOff").toString());
                    BigDecimal discountAmount = proratedDifference
                            .multiply(percentOff)
                            .divide(new BigDecimal(100), 10, RoundingMode.HALF_UP);
                    proratedDifference = proratedDifference.subtract(discountAmount);
                }
                
                // 固定金额折扣
                if (discount.containsKey("amountOff")) {
                    BigDecimal amountOff = new BigDecimal(discount.get("amountOff").toString());
                    // 按比例计算折扣的固定金额
                    BigDecimal proratedAmountOff = amountOff.multiply(remainingRatio).setScale(0, RoundingMode.HALF_UP);
                    proratedDifference = proratedDifference.subtract(proratedAmountOff);
                    if (proratedDifference.compareTo(BigDecimal.ZERO) < 0) {
                        proratedDifference = BigDecimal.ZERO;
                    }
                }
            }
        }
        
        return proratedDifference.max(BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP).longValue();
    }
    
    @Override
    public SubscriptionPayType getSubscriptionPayType() {
        return SubscriptionPayType.ANTOM;
    }

    @Override
    public String getAppCode() {
        return AppEnum.TEXT2GO.getCode();
    }

    public Boolean cancel(Long userId) {
        return Boolean.TRUE;
    }

    /**
     * 处理订阅升级
     * 立即升级订阅并计算需要支付的差价
     *
     * @param userId 用户ID
     * @param currentSubscriptionId 当前订阅ID
     * @param newPlanId 新套餐ID
     * @return 升级结果
     */
    public SingleResult<Map<String, Object>> upgradeSubscription(Long userId, String currentSubscriptionId, Long newPlanId) {
        try {
            log.info("AntomSubManagerForT2g.upgradeSubscription, userId={}, currentSubscriptionId={}, newPlanId={}", 
                userId, currentSubscriptionId, newPlanId);
            
            // 1. 获取当前订阅信息
            // 这里模拟从数据库或Antom API获取订阅信息
            Map<String, Object> currentSubscription = new HashMap<>();
            // 模拟当前套餐信息
            currentSubscription.put("id", currentSubscriptionId);
            currentSubscription.put("userId", userId);
            currentSubscription.put("price", 1999L); // 当前价格19.99美元（分）
            currentSubscription.put("interval", "month");
            currentSubscription.put("periodStart", System.currentTimeMillis() / 1000 - 86400 * 15); // 15天前开始
            currentSubscription.put("periodEnd", System.currentTimeMillis() / 1000 + 86400 * 15); // 15天后结束
            
            // 2. 获取新套餐信息
            // 从Stripe获取新套餐价格信息保持一致性
            Stripe.apiKey = SwitchConfig.stripeApiKey;
            String lookupKey = SwitchConfig.t2gPlanId2LookupKey.get(newPlanId.toString());
            Price newPrice = fetchPriceFromStripe(lookupKey);
            
            Long newPriceAmount = newPrice.getUnitAmount();
            
            // 3. 计算升级差价
            Long prorationTime = System.currentTimeMillis() / 1000;
            Long proratedDifference = calculateUpgradeProration(
                (Long) currentSubscription.get("price"),
                newPriceAmount,
                (Long) currentSubscription.get("periodStart"),
                (Long) currentSubscription.get("periodEnd"),
                null, // 暂不考虑折扣
                prorationTime
            );
            
            // 4. 创建升级支付订单
            // 这里应该调用Antom API创建实际的支付订单，这里只做模拟
            Map<String, Object> upgradeResult = new HashMap<>();
            upgradeResult.put("userId", userId);
            upgradeResult.put("oldSubscriptionId", currentSubscriptionId);
            upgradeResult.put("newPlanId", newPlanId);
            upgradeResult.put("prorationAmount", proratedDifference);
            upgradeResult.put("currency", newPrice.getCurrency());
            upgradeResult.put("orderId", "antom_order_" + System.currentTimeMillis());
            
            // 5. 返回结果
            log.info("AntomSubManagerForT2g.upgradeSubscription success, result={}", 
                JSONObject.toJSONString(upgradeResult));
            return SingleResult.buildSuccess(upgradeResult);
            
        } catch (Exception e) {
            log.error("AntomSubManagerForT2g.upgradeSubscription error ", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    /**
     * 预览订阅升级的价格差异
     * 
     * @param userId 用户ID
     * @param currentSubscriptionId 当前订阅ID
     * @param newPlanId 新套餐ID
     * @return 预览结果
     */
    public SingleResult<Map<String, Object>> previewUpgrade(Long userId, String currentSubscriptionId, Long newPlanId) {
        try {
            log.info("AntomSubManagerForT2g.previewUpgrade, userId={}, currentSubscriptionId={}, newPlanId={}", 
                userId, currentSubscriptionId, newPlanId);
            
            // 1. 获取当前订阅信息
            // 这里模拟从数据库或Antom API获取订阅信息
            Map<String, Object> currentSubscription = new HashMap<>();
            // 模拟当前套餐信息
            currentSubscription.put("id", currentSubscriptionId);
            currentSubscription.put("userId", userId);
            currentSubscription.put("price", 1999L); // 当前价格19.99美元（分）
            currentSubscription.put("interval", "month");
            currentSubscription.put("intervalCount", 1);
            currentSubscription.put("planName", "标准套餐");
            currentSubscription.put("periodStart", System.currentTimeMillis() / 1000 - 86400 * 15); // 15天前开始
            currentSubscription.put("periodEnd", System.currentTimeMillis() / 1000 + 86400 * 15); // 15天后结束
            
            // 2. 获取新套餐信息
            // 从Stripe获取新套餐价格信息保持一致性
            Stripe.apiKey = SwitchConfig.stripeApiKey;
            String lookupKey = SwitchConfig.t2gPlanId2LookupKey.get(newPlanId.toString());
            Price newPrice = fetchPriceFromStripe(lookupKey);
            Long newPriceAmount = newPrice.getUnitAmount();
            
            // 3. 判断是升级还是降级
            boolean isUpgrade = newPriceAmount > (Long) currentSubscription.get("price");
            
            // 4. 准备预览结果
            Map<String, Object> previewResult = new HashMap<>();
            previewResult.put("userId", userId);
            previewResult.put("currentSubscriptionId", currentSubscriptionId);
            previewResult.put("newPlanId", newPlanId);
            previewResult.put("currency", newPrice.getCurrency());
            
            // 设置基本信息
            previewResult.put("currentAmount", currentSubscription.get("price"));
            previewResult.put("nextAmount", newPriceAmount);
            previewResult.put("currentInterval", currentSubscription.get("interval"));
            previewResult.put("currentIntervalCount", currentSubscription.get("intervalCount"));
            previewResult.put("newInterval", newPrice.getRecurring().getInterval());
            previewResult.put("newIntervalCount", newPrice.getRecurring().getIntervalCount());
            previewResult.put("curPlanName", currentSubscription.get("planName"));
            previewResult.put("newPlanName", newPrice.getNickname());
            previewResult.put("currentPeriodStart", currentSubscription.get("periodStart"));
            previewResult.put("currentPeriodEnd", currentSubscription.get("periodEnd"));
            
            if (isUpgrade) {
                // 升级情况：计算需要立即支付的金额
                Long prorationTime = System.currentTimeMillis() / 1000;
                Long proratedDifference = calculateUpgradeProration(
                    (Long) currentSubscription.get("price"),
                    newPriceAmount,
                    (Long) currentSubscription.get("periodStart"),
                    (Long) currentSubscription.get("periodEnd"),
                    null, // 暂不考虑折扣
                     prorationTime
                );
                previewResult.put("payType", "now");
                previewResult.put("immediateCharge", proratedDifference);
            } else {
                // 降级情况：在当前周期结束后生效
                previewResult.put("payType", "periodEnd");
                previewResult.put("immediateCharge", 0L);
            }
            
            // 5. 返回结果
            log.info("AntomSubManagerForT2g.previewUpgrade success, result={}", 
                JSONObject.toJSONString(previewResult));
            return SingleResult.buildSuccess(previewResult);
            
        } catch (Exception e) {
            log.error("AntomSubManagerForT2g.previewUpgrade error ", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }
}
