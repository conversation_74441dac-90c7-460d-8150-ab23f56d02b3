package com.alibaba.copilot.enabler.service.subscription.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.boot.tools.lock.Lock;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.robot.dto.TradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionSource;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.request.CancelSubscribedPlanRequest;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionManageService;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionQueryService;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.gateway.ShoplazzaGateway;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.CacheRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.payment.metaq.trade.TradeStartProducer;
import com.alibaba.copilot.enabler.service.subscription.constant.ShoplazzaSubscriptionStatus;
import com.alibaba.copilot.enabler.service.subscription.factory.*;
import com.alibaba.copilot.enabler.service.subscription.manager.BizSubscriptionContext;
import com.alibaba.copilot.enabler.service.subscription.manager.SubscriptionManagerFactory;
import com.alibaba.copilot.enabler.service.subscription.manager.adic.StripeSubManagerForADIC;
import com.alibaba.copilot.enabler.service.subscription.manager.t2g.StripeSubManagerForT2g;
import com.alibaba.copilot.enabler.service.subscription.service.pic.PicSubscriptionService;
import com.alibaba.fastjson.JSON;
import com.stripe.exception.StripeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2024/1/16
 */
@Slf4j
@Service
public class SubscriptionManageServiceImpl implements SubscriptionManageService {

    private static final String SHOPLAZZA_SUBSCRIPTION_UPDATE_CACHE_KEY = "shoplazza_subscription_update_cache_key";

    @Resource
    private SubscriptionService subscriptionService;

    @Resource
    private OrderDomainService orderDomainService;

    @Autowired
    private SubscriptionQueryService subscriptionQueryService;

    @Resource
    private TradeStartProducer tradeStartProducer;

    @Resource
    private CreatePaymentSessionDTOBuilder createPaymentSessionDTOBuilder;

    @Resource
    private SubscribeContextBuilder subscribeContextBuilder;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private StripeSubManagerForT2g stripeSubManagerForT2g;

    @Autowired
    private ShoplazzaGateway shoplazzaGateway;

    @Resource
    private CacheRepository cacheRepository;

    @Resource
    private UserRepository userRepository;

    @Autowired
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private PicSubscriptionService picSubscriptionService;

    @Resource
    private UserAppRelationService userAppRelationService;

    @Resource
    private StripeSubManagerForADIC stripeSubManagerForADIC;

    @Monitor(name = "[Subscription Manage Service] 订阅域-订阅计划", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    @Override
    public SingleResult<SubscribePlanResultDTO> subscribePlan(SubscribePlanDTO subscribePlanDTO) {
        Assertor.assertNonNull(subscribePlanDTO, "subscribePlanDTO can't be null");

        if (SubscriptionSource.Shoplazza.name().equals(subscribePlanDTO.getSubscriptionSource())) {
            WebSubscriptionContext webSubscriptionContext = WebSubscriptionContext.builder()
                    .subscribePlanDTO(subscribePlanDTO)
                    .build();

            WebSubscriptionStrategy strategy = WebSubscriptionStrategyFactory.getStrategy(webSubscriptionContext);
            if (strategy != null) {
                SubscribePlanResultDTO resultDTO = strategy.subscribe(webSubscriptionContext);
                return SingleResult.buildSuccess(resultDTO);
            } else {
                throw new BizException(ErrorCode.SYS_ERROR, "strategy not exist");
            }
        }

        BizSubscriptionContext context = new BizSubscriptionContext();
        context.setSubscribePlanDTO(subscribePlanDTO);

        return SubscriptionManagerFactory
                .getManagerNotNull(context)
                .subscribe(context);
    }



    /**
     * 取消订阅
     */
    @Monitor(name = "[Subscription Manage Service] 订阅域-取消订阅", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelSubscribedPlan(CancelSubscribedPlanRequest request, Long userId) {
        log.info("cancelSubscribedPlan, request={}, userId={}", JSON.toJSONString(request), userId);

        if (SubscriptionSource.Shoplazza.name().equals(request.getSubscriptionSource())) {
            SubscribePlanDTO subscribePlanDTO = new SubscribePlanDTO();
            subscribePlanDTO.setAppCode(request.getAppCode());
            subscribePlanDTO.setUserId(userId);
            subscribePlanDTO.setShopDomain(request.getShopDomain());
            subscribePlanDTO.setAccessToken(request.getAccessToken());
            subscribePlanDTO.setSubscriptionSource(request.getSubscriptionSource());

            WebSubscriptionContext webSubscriptionContext = WebSubscriptionContext.builder()
                    .subscribePlanDTO(subscribePlanDTO)
                    .build();

            WebSubscriptionStrategy strategy = WebSubscriptionStrategyFactory.getStrategy(webSubscriptionContext);
            if (strategy != null) {
                return strategy.cancelSubscribedPlan(webSubscriptionContext);
            } else {
                throw new BizException(ErrorCode.SYS_ERROR, "strategy not exist");
            }
        }

        String appCode = request.getAppCode();
        if (AppEnum.ADIC.getCode().equals(appCode)) {
            return stripeSubManagerForADIC.cancelOrderForADIC(userId);
        }

        if (AppEnum.TEXT2GO.getCode().equals(request.getAppCode())) {
            return stripeSubManagerForT2g.cancel(userId);
        }

        // 根据配置判断是否需要强制退款
        boolean forceRefund = CollectionUtil.contains(SwitchConfig.userWhitelistForAllowRefund, userId);
        return orderDomainService.cancelCurrentEffectOrder(appCode, userId, forceRefund);
    }

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    @Override
    public Boolean switchSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionManageServiceImpl.switchSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));

        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(subscriptionOrderDTO.getId());
        log.info("SubscriptionManageServiceImpl.switchSubscriptionOrder, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));
        if (subscriptionOrder == null) {
            return false;
        }
        SubscriptionPlan subscriptionPlanNew = subscriptionPlanRepository.queryByPlanId(subscriptionOrderDTO.getSubscriptionPlanId(), Boolean.FALSE);
        if (subscriptionPlanNew == null) {
            return false;
        }

        SubscriptionOrderAttributes subscriptionOrderAttributes = subscriptionOrder.getAttributes();

        long planDays = TimeUtils.calculateDayCount(subscriptionPlanNew.getDuration(), subscriptionPlanNew.getDurationUnit());
        subscriptionOrderAttributes.setPlanDays(planDays);

        Date startTime = subscriptionOrder.getPerformStartTime();
        Date endTime = DateUtils.addDays(startTime, (int) planDays);
        Boolean includeTrial = false;
        long totalTrailDay = 0;
        Long trialDuration = subscriptionPlanNew.getTrialDuration();
        String trialDurationUnit = subscriptionPlanNew.getTrialDurationUnit();
        if (trialDuration != null && StringUtils.isNotBlank(trialDurationUnit)) {
            totalTrailDay = TimeUtils.calculateDayCount(trialDuration, trialDurationUnit);
        }
        if (totalTrailDay > 0) {
            subscriptionOrderAttributes.setTrialDays(Long.parseLong(String.valueOf(totalTrailDay)));
            endTime = DateUtils.addDays(endTime, (int) totalTrailDay);
            includeTrial = true;
        }

        subscriptionOrder.setSubscriptionPlanId(subscriptionPlanNew.getId());
        subscriptionOrder.setSubscriptionPlanName(subscriptionPlanNew.getName());
        subscriptionOrder.setPlanPrice(subscriptionPlanNew.getPrice());
        subscriptionOrder.setNextRenewalTime(endTime);
        subscriptionOrder.setPerformEndTime(endTime);
        subscriptionOrder.setIsIncludeTrial(includeTrial);
        subscriptionOrder.setGmtModified(new Date());

        log.info("SubscriptionManageServiceImpl.switchSubscriptionOrder, before, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
        log.info("SubscriptionManageServiceImpl.switchSubscriptionOrder, succeed, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));

        return true;
    }

    /**
     * 处理订阅回调
     *
     * @param webSubscriptionEventDTO
     * @return
     */
    @Override
    public Boolean handleSubscriptionsUpdate(WebSubscriptionEventDTO webSubscriptionEventDTO) {
        log.info("SubscriptionManageServiceImpl.handleSubscriptionsUpdate, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));

        String deduplicationId = webSubscriptionEventDTO.getDeduplicationId();
        String key = SHOPLAZZA_SUBSCRIPTION_UPDATE_CACHE_KEY + deduplicationId;

        try (Lock lock = cacheRepository.getLock(SHOPLAZZA_SUBSCRIPTION_UPDATE_CACHE_KEY, deduplicationId)) {
            boolean locked = lock.tryLock();
            log.info("SubscriptionManageServiceImpl.handleSubscriptionsUpdate, lock, deduplicationId={} locked={}", deduplicationId, locked);
            // 未获得锁
            if (!locked) {
                return true;
            }
            // 幂等处理
            if (cacheRepository.get(key) != null) {
                return true;
            }

            SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOuterSubscriptionId(null, webSubscriptionEventDTO.getId());
            if (subscriptionOrder == null) {
                log.info("SubscriptionManageServiceImpl.handleSubscriptionsUpdate, getByOuterSubscriptionId error, outerSubscriptionId={}", webSubscriptionEventDTO.getId());
                throw new BizException(ErrorCode.SYS_ERROR, "getByOuterSubscriptionId error");
            }
            log.info("SubscriptionManageServiceImpl.handleSubscriptionsUpdate, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));

            ShoplazzaSubscriptionStatus status = ShoplazzaSubscriptionStatus.valueOf(webSubscriptionEventDTO.getStatus());

            switch (status) {
                case pending:
                    break;
                case active:
                    activeHandle(subscriptionOrder, webSubscriptionEventDTO);
                    break;
                case paid_failed:
                case declined:
                case expired:
                    failHandle(subscriptionOrder, webSubscriptionEventDTO);
                    break;
                case cancelled:
                    cancelHandle(subscriptionOrder, webSubscriptionEventDTO);
                    break;
                default:
                    break;
            }

            cacheRepository.set(key, 1, 60 * 60 * 24 * 7L);
        } catch (Exception e) {
            log.error("SubscriptionManageServiceImpl.handleSubscriptionsUpdate exception, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO), e);
            return false;
        }

        return true;
    }

    @Override
    public SingleResult<SubscriptionPreviewResultDTO> previewSubscribe(SubscribePlanDTO subscribePlanDTO) {
        try {
            return SingleResult.buildSuccess(stripeSubManagerForT2g.preview(subscribePlanDTO));
        } catch (StripeException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 订阅成功
     *
     * @param subscriptionOrder
     * @param webSubscriptionEventDTO
     */
    @Transactional(rollbackFor = Exception.class)
    private void activeHandle(SubscriptionOrder subscriptionOrder, WebSubscriptionEventDTO webSubscriptionEventDTO) {
        log.info("SubscriptionManageServiceImpl.activeHandle, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));

        if (!SubscriptionOrderStatus.IN_EFFECT.equals(subscriptionOrder.getStatus())) {
            subscriptionOrder.setGmtModified(new Date());
            subscriptionOrder.setStatus(SubscriptionOrderStatus.IN_EFFECT);
            subscriptionOrder.setSubscriptionPayType(SubscriptionPayType.Shoplazza);
        } else {
            subscriptionOrder.setGmtModified(new Date());
            SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(subscriptionOrder.getSubscriptionPlanId(), false);
            long diffTime = TimeUtils.calculateDayCount(subscriptionPlan.getDuration(), subscriptionPlan.getDurationUnit()) * 24 * 60 * 60 * 1000L;
            subscriptionOrder.setNextRenewalTime(new Date(subscriptionOrder.getNextRenewalTime().getTime() + diffTime));
            subscriptionOrder.setPerformStartTime(new Date(subscriptionOrder.getPerformStartTime().getTime() + diffTime));
            subscriptionOrder.setPerformEndTime(new Date(subscriptionOrder.getPerformEndTime().getTime() + diffTime));
        }

        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        TradeRecordDTO tradeRecordDTO = new TradeRecordDTO();
        tradeRecordDTO.setSubscriptionOrderId(subscriptionOrder.getId());
        tradeRecordDTO.setTradeAmount(new BigDecimal(webSubscriptionEventDTO.getPrice()));
        tradeRecordDTO.setTradeDirection(TradeDirection.FORWARD.name());
        tradeRecordDTO.setStatus(TradeRecordStatus.SUCC.name());
        tradeRecordDTO.setPaymentMethod(PaymentMethodEnum.SHOPLAZZA.name());
        tradeRecordDTO.setSubscriptionPlanId(subscriptionOrder.getSubscriptionPlanId());
        tradeRecordDTO.setTradeTime(webSubscriptionEventDTO.getUpdatedAt());
        subscriptionService.createTradeRecord(tradeRecordDTO);

        log.info("SubscriptionManageServiceImpl.activeHandle, succeed, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));
    }

    /**
     * 订阅失败
     *
     * @param subscriptionOrder
     * @param webSubscriptionEventDTO
     */
    private void failHandle(SubscriptionOrder subscriptionOrder, WebSubscriptionEventDTO webSubscriptionEventDTO) {
        log.info("SubscriptionManageServiceImpl.failHandle, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));

        SubscriptionOrderDTO subscriptionOrderDTO = new SubscriptionOrderDTO();
        subscriptionOrderDTO.setId(subscriptionOrder.getId());
        subscriptionOrderDTO.setStatus(SubscriptionOrderStatus.EXPIRED);
        subscriptionService.updateSubscriptionOrder(subscriptionOrderDTO);

        log.info("SubscriptionManageServiceImpl.failHandle, succeed, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));
    }

    /**
     * 取消订阅
     *
     * @param subscriptionOrder
     * @param webSubscriptionEventDTO
     */
    private void cancelHandle(SubscriptionOrder subscriptionOrder, WebSubscriptionEventDTO webSubscriptionEventDTO) {
        log.info("SubscriptionManageServiceImpl.cancelHandle, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));

        SubscriptionOrderDTO subscriptionOrderDTO = new SubscriptionOrderDTO();
        subscriptionOrderDTO.setId(subscriptionOrder.getId());
        subscriptionOrderDTO.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
        subscriptionService.updateSubscriptionOrder(subscriptionOrderDTO);

        log.info("SubscriptionManageServiceImpl.cancelHandle, succeed, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));
    }
}
