package com.alibaba.copilot.enabler.service.subscription.manager.t2g;


import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.facade.Text2GoSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.Text2GoSubscriptionService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.copilot.enabler.service.subscription.constant.SubscriptionConstants;
import com.alibaba.copilot.enabler.service.subscription.manager.AbstractSubscriptionManager;
import com.alibaba.copilot.enabler.service.subscription.manager.BizSubscriptionContext;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.model.checkout.SessionCollection;
import com.stripe.param.*;
import com.stripe.param.checkout.SessionCreateParams;
import com.stripe.param.checkout.SessionCreateParams.UiMode;
import com.stripe.param.checkout.SessionListParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Controller
@Slf4j
public class StripeSubManagerForT2g extends AbstractSubscriptionManager {
    @Resource
    private StripeService stripeService;
    @Resource
    private SubscriptionService subscriptionService;
    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;
    @Resource
    private Text2GoSubscriptionService text2GoSubscriptionService;

    @Override
    protected SingleResult<SubscribePlanResultDTO> doSubscribe(BizSubscriptionContext context) {
        try {
            SubscribePlanResultDTO stripeOrder = createStripeOrder(context.getSubscribePlanDTO());
            return  SingleResult.buildSuccess(stripeOrder);
        } catch (Exception e) {
            log.error("StripeSubManagerForT2g error ", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    @Override
    public SubscriptionPayType getSubscriptionPayType() {
        return SubscriptionPayType.STRIPE;
    }



    private SubscribePlanResultDTO createStripeOrder(SubscribePlanDTO subscribePlanDTO) throws StripeException {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Long planId = subscribePlanDTO.getPlanId();
        String key = SwitchConfig.t2gPlanId2LookupKey.get(planId.toString());

        // 1. 并行执行所有可能的基础操作，避免串行等待
        CompletableFuture<PriceCollection> pricesFuture = CompletableFuture.supplyAsync(() -> {
            try {
                PriceListParams priceParams = PriceListParams.builder()
                        .addLookupKey(key)
                        .build();
                return Price.list(priceParams);
            } catch (StripeException e) {
                throw new CompletionException(e);
            }
        });

        CompletableFuture<TrialDurationDTO> trialFuture = CompletableFuture.supplyAsync(() ->
            text2GoSubscriptionService.subscriptionIsTrial(subscribePlanDTO.getUserId())
        );

        CompletableFuture<String> customerIdFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return stripeService.getOrCreateStripeCustomer(subscribePlanDTO.getAppCode(), subscribePlanDTO.getUserId());
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        });

        CompletableFuture<SubscriptionPlan> planFuture = CompletableFuture.supplyAsync(() ->
            subscriptionPlanRepository.queryByPlanId(planId, false)
        );

        try {
            // 先获取客户ID - 这是必须的，用于后续查询订阅
            String stripeCustomerId = customerIdFuture.get();

            // 2. 立即启动订阅查询和会话清理 - 这两个操作可以并行执行
            CompletableFuture<SubscriptionCollection> subscriptionsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    SubscriptionListParams subscritionListParam = SubscriptionListParams.builder()
                            .setCustomer(stripeCustomerId)
                            .build();
                    return Subscription.list(subscritionListParam);
                } catch (StripeException e) {
                    throw new CompletionException(e);
                }
            });

            CompletableFuture<Void> cleanupFuture = CompletableFuture.runAsync(() -> {
                try {
                    // 将 stripe 侧的未付款session设置为取消
                    SessionCollection sessions = Session.list(
                            SessionListParams.builder()
                                    .setCustomer(stripeCustomerId)
                                    .setStatus(SessionListParams.Status.OPEN)
                                    .build()
                    );
                    for (Session session : sessions.getData()) {
                        session.expire();
                    }
                } catch (Exception e) {
                    log.warn("Failed to expire old sessions", e);
                }
            });

            // 获取其他必要信息
            SubscriptionPlan subscriptionPlan = planFuture.get();
            PriceCollection prices = pricesFuture.get();
            Price newPrice = prices.getData().get(0);

            // 获取订阅集合
            SubscriptionCollection subscriptions = subscriptionsFuture.get();

            // 3. 判断是更新还是新建订阅 - 保持原有业务逻辑
            if (CollectionUtils.isNotEmpty(subscriptions.getData()) && subscriptions.getData().stream()
                    .anyMatch(s -> Lists.newArrayList("trialing", "active").contains(s.getStatus()))) {
                Subscription currentSubscription = subscriptions.getData().get(0);
                Price curPrice = currentSubscription.getItems().getData().get(0).getPrice();
                Long curUnitAmount = curPrice.getUnitAmount();
                Long newUnitAmount = newPrice.getUnitAmount();

                // 如果当前订阅已经有了订阅周期，需要解绑
                if (StringUtils.isNotBlank(currentSubscription.getSchedule())) {
                    SubscriptionSchedule schedule = SubscriptionSchedule.retrieve(currentSubscription.getSchedule());
                    if (Lists.newArrayList("active", "not_started").contains(schedule.getStatus())) {
                        schedule.release();
                    }
                }

                boolean upgrade = curUnitAmount != null && newUnitAmount != null && newUnitAmount >= curUnitAmount;

                if (upgrade) {
                    SubscriptionUpdateParams.Builder updateBuilder = SubscriptionUpdateParams.builder()
                            .addItem(SubscriptionUpdateParams.Item.builder()
                                    .setId(currentSubscription.getItems().getData().get(0).getId())
                                    .setPrice(newPrice.getId())
                                    .setQuantity(1L)
                                    .build())
                            .setProrationDate(culSubscriptionProrationDate(currentSubscription))
                            .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.CREATE_PRORATIONS)
                            .setPaymentBehavior(SubscriptionUpdateParams.PaymentBehavior.ERROR_IF_INCOMPLETE)
                            .setCancelAtPeriodEnd(false);

                            if ("trialing".equals(currentSubscription.getStatus())) {
                                updateBuilder.setTrialEnd(SubscriptionUpdateParams.TrialEnd.NOW);
                            }

                            Subscription updatedSubscription = currentSubscription.update(updateBuilder.build());
                            InvoiceCreateParams invoiceParams = InvoiceCreateParams.builder()
                                    .setCustomer(updatedSubscription.getCustomer())
                                    .setSubscription(updatedSubscription.getId())
                                    .setAutoAdvance(true)
                                    .build();
                            Invoice invoice = Invoice.create(invoiceParams);
                            invoice.pay();
                            return new SubscribePlanResultDTO();
                        } else {
                            // 原套餐价格更贵, 等到原套餐结束后生效
                            SubscriptionScheduleCreateParams scheduleCreateParams = SubscriptionScheduleCreateParams.builder()
                                    .setFromSubscription(currentSubscription.getId())
                                    .build();
                            SubscriptionSchedule schedule = SubscriptionSchedule.create(scheduleCreateParams);
                            SubscriptionSchedule.retrieve(schedule.getId());
                            SubscriptionScheduleUpdateParams paramsUpdate =
                                    SubscriptionScheduleUpdateParams.builder()
                                            // 保持第一段周期（现在的）
                                            .addPhase(
                                                    SubscriptionScheduleUpdateParams.Phase.builder()
                                                            .addItem(
                                                                    SubscriptionScheduleUpdateParams.Phase.Item.builder()
                                                                            .setPrice(schedule.getPhases().get(0).getItems().get(0).getPrice())
                                                                            .setQuantity(schedule.getPhases().get(0).getItems().get(0).getQuantity())
                                                                            .build()
                                                            )
                                                            .setStartDate(schedule.getPhases().get(0).getStartDate())
                                                            .setEndDate(schedule.getPhases().get(0).getEndDate())
                                                            .setTrialEnd(schedule.getPhases().get(0).getTrialEnd())
                                                            .build()
                                            )
                                            .addPhase(
                                                    SubscriptionScheduleUpdateParams.Phase.builder()
                                                            .addItem(
                                                                    SubscriptionScheduleUpdateParams.Phase.Item.builder()
                                                                            .setPrice(newPrice.getId())
                                                                            .setQuantity(1L)
                                                                            .build()
                                                            )
                                                            .setStartDate(schedule.getPhases().get(0).getEndDate())
                                                            .build()
                                            )
                                            .setEndBehavior(SubscriptionScheduleUpdateParams.EndBehavior.RELEASE)
                                            .build();
                            schedule.update(paramsUpdate);
                            return new SubscribePlanResultDTO();
                        }
                    }

                    // 4. 创建新订阅 - 这部分可以并行获取试用期信息


                    // 并行创建订单和获取试用信息
                    CompletableFuture<Long> orderIdFuture = CompletableFuture.supplyAsync(() -> {
                                return createSubscribeOrder(subscribePlanDTO, planId, subscriptionPlan);
                            }
                    );

                    // 添加元数据信息时获取订单ID
                    Long subscriptionOrderId = orderIdFuture.get();

                    // 添加订阅的元数据信息
                    Map<String, String> metaDataMap = new HashMap<>();
                    metaDataMap.put(SubscriptionConstants.META_APP_CODE, String.valueOf(subscribePlanDTO.getAppCode()));
                    metaDataMap.put(SubscriptionConstants.META_USER_ID, String.valueOf(subscribePlanDTO.getUserId()));
                    metaDataMap.put(SubscriptionConstants.META_SUB_ID, String.valueOf(subscriptionOrderId));
                    metaDataMap.put(SubscriptionConstants.META_SHARE_CODE, subscribePlanDTO.getShareCode());
                    if (StringUtils.isNotBlank(subscribePlanDTO.getClientIp())) {
                        metaDataMap.put(SubscriptionConstants.META_CLIENT_IP, subscribePlanDTO.getClientIp());
                    }

                    // 获取当前时间戳
                    Instant now = Instant.now();
                    Instant oneHourLater = now.plusSeconds(3600);
                    long timestampOneHourLater = oneHourLater.getEpochSecond();

                    // 获取试用期信息
                    TrialDurationDTO trialDurationDTO = trialFuture.get();

                    SubscribePlanResultDTO subscribePlanResultDTO = new SubscribePlanResultDTO();

                    // UI模式设置
                    String uiModeValue = subscribePlanDTO.getStripeUiMode();
                    if (uiModeValue != null) {
                        log.info("Found stripeUiMode value: {}", uiModeValue);
                    }

                    UiMode uiMode = UiMode.HOSTED;
                    if (uiModeValue != null && uiModeValue.equalsIgnoreCase(UiMode.EMBEDDED.getValue())) {
                        uiMode = UiMode.EMBEDDED;
                    }

                    // 构建Session参数
                    SessionCreateParams.SubscriptionData.Builder subDataBuilder = SessionCreateParams.SubscriptionData.builder();
                    if (trialDurationDTO != null && Boolean.TRUE.equals(trialDurationDTO.getIsTrial()) && trialDurationDTO.getRemainTrialDay() != null) {
                        subDataBuilder.setTrialPeriodDays(trialDurationDTO.getRemainTrialDay());
                    }

                    SessionCreateParams.Builder sessionCreateParamBuilder = SessionCreateParams.builder()
                        .setUiMode(uiMode)
                        .addLineItem(SessionCreateParams.LineItem.builder()
                            .setPrice(newPrice.getId())
                            .setQuantity(1L)
                            .build())
                        .setCustomer(stripeCustomerId)
                        .setSubscriptionData(subDataBuilder
                            .putAllMetadata(metaDataMap)
                            .build())
                        .putAllMetadata(metaDataMap)
                        .setExpiresAt(timestampOneHourLater)
                        .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
                        .setPaymentMethodConfiguration(SwitchConfig.stripePaymentMethodConfig);

                    // 根据UI模式设置URL参数
                    if (UiMode.EMBEDDED.equals(uiMode)) {
                        sessionCreateParamBuilder.setReturnUrl(subscribePlanDTO.getRedirectUrl());
                    } else {
                        sessionCreateParamBuilder.setSuccessUrl(subscribePlanDTO.getRedirectUrl());
                    }

                    // 处理折扣码
                    if (StringUtils.isBlank(subscribePlanDTO.getDiscountCode())) {
                        sessionCreateParamBuilder.setAllowPromotionCodes(true);
                    } else {
                        sessionCreateParamBuilder.addDiscount(
                            SessionCreateParams.Discount.builder()
                                .setPromotionCode(subscribePlanDTO.getDiscountCode())
                                .build()
                        );
                    }

                    if (SubscriptionConstants.SHARE_CODE_SECOND_MONTH_FREE
                            .equals(subscribePlanDTO.getShareCode()) && "month".equals(newPrice.getRecurring().getInterval())) {
                        sessionCreateParamBuilder.addLineItem(SessionCreateParams.LineItem.builder()
                                        .setPriceData(SessionCreateParams.LineItem.PriceData.builder()
                                                .setCurrency("usd")
                                                .setUnitAmount(0L)
                                                .setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                                        .setName("Free Bonus Month")
                                                        .build())
                                                .build())
                                        .setQuantity(1L).build());
                    }

                    // 创建session并设置结果
                    // 注：这里不等待cleanupFuture完成，因为它不影响主流程
                    Session session = Session.create(sessionCreateParamBuilder.build());

                    // 设置返回URL
                    if (UiMode.EMBEDDED.equals(uiMode)) {
                        subscribePlanResultDTO.setSubscriptionUrl(session.getClientSecret());
                    } else {
                        subscribePlanResultDTO.setSubscriptionUrl(session.getUrl());
                    }

                    subscribePlanResultDTO.setOrderId(subscriptionOrderId);
                    log.info("StripeSubscriptionStrategy.doSubscribeByStripe, userId={}, input={}, session={}",
                        subscribePlanDTO.getUserId(), JSONObject.toJSONString(subscribePlanDTO), JSON.toJSONString(session));

                    return subscribePlanResultDTO;
                } catch (InterruptedException | ExecutionException e) {
                    throw new StripeException(e.getMessage(), null, null, 0, e) {
                        @Override
                        public StripeError getStripeError() {
                            return null;
                        }
                    };
                }
            }


    Long createSubscribeOrder(SubscribePlanDTO subscribePlanDTO, Long planId, SubscriptionPlan subscriptionPlan) {
        SubscriptionOrderDTO subscriptionOrderDTO = new SubscriptionOrderDTO();
        subscriptionOrderDTO.setUserId(subscribePlanDTO.getUserId());
        subscriptionOrderDTO.setSubscriptionPlanId(planId);
        subscriptionOrderDTO.setSubscriptionPlanName(subscriptionPlan.getName());
        subscriptionOrderDTO.setAppCode(subscribePlanDTO.getAppCode());
        subscriptionOrderDTO.setEmail(subscribePlanDTO.getEmail());
        subscriptionOrderDTO.setPlanPrice(BigDecimal.ZERO);
        subscriptionOrderDTO.setPerformStartTime(new Date());
        subscriptionOrderDTO.setPerformEndTime(new Date());
        return subscriptionService.createSubscriptionOrderNoPlan(subscriptionOrderDTO);
    }

    public SubscriptionPreviewResultDTO preview(SubscribePlanDTO subscribePlanDTO) throws StripeException {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Long planId = subscribePlanDTO.getPlanId();
        String key = SwitchConfig.t2gPlanId2LookupKey.get(planId.toString());

        PriceListParams priceParams = PriceListParams.builder()
                .addLookupKey(key)
                .build();
        PriceCollection prices = Price.list(priceParams);
        String stripeCustomerId = stripeService.getOrCreateStripeCustomer(subscribePlanDTO.getAppCode(), subscribePlanDTO.getUserId());

        // 获取该客户的所有订阅
        SubscriptionListParams subscritionListParam = SubscriptionListParams.builder()
                .setCustomer(stripeCustomerId) // 根据 customerId 查询
                .addAllExpand(Lists.newArrayList("data.discounts", "data.discounts.coupon"))
                .build();
        SubscriptionCollection subscriptions = Subscription.list(subscritionListParam);

        if (CollectionUtils.isEmpty(subscriptions.getData()) && subscriptions.getData().stream()
                .anyMatch(s -> Lists.newArrayList("trialing", "active").contains(s.getStatus()))) {
            throw new RuntimeException("there is no subscription, no need to preview");
        }
        SubscriptionPreviewResultDTO resultDTO = new SubscriptionPreviewResultDTO();
        Subscription currentSubscription = subscriptions.getData().get(0);
        Price curPrice = currentSubscription.getItems().getData().get(0).getPrice();
        Price newPrice = prices.getData().get(0);
        if (StringUtils.equals(curPrice.getId(), newPrice.getId())) {
            throw new RuntimeException("plan equals");
        }
        Long curUnitAmount = curPrice.getUnitAmount();
        Long newUnitAmount = newPrice.getUnitAmount();

        boolean upgrade = curUnitAmount != null && newUnitAmount != null && newUnitAmount >= curUnitAmount;

        // 这些不管是升级降级都一样
        resultDTO.setCurrency(curPrice.getCurrency());
        resultDTO.setNextAmount(newPrice.getUnitAmount());
        resultDTO.setCurrentAmount(curPrice.getUnitAmount());
        resultDTO.setCurrentInterval(curPrice.getRecurring().getInterval());
        resultDTO.setCurrentIntervalCount(curPrice.getRecurring().getIntervalCount());
        resultDTO.setNewInterval(newPrice.getRecurring().getInterval());
        resultDTO.setNewIntervalCount(newPrice.getRecurring().getIntervalCount());
        resultDTO.setCurPlanName(curPrice.getNickname());
        resultDTO.setNewPlanName(newPrice.getNickname());
        resultDTO.setCurrentPeriodStart(currentSubscription.getCurrentPeriodStart());
        resultDTO.setCurrentPeriodEnd(currentSubscription.getCurrentPeriodEnd());


        if (upgrade) {
            resultDTO.setPayType("now");
            // 注意这段逻辑一定要和正式订阅的部分保持一样。
            InvoiceUpcomingParams.Builder upcommingParamsBuilder = InvoiceUpcomingParams.builder();

            upcommingParamsBuilder
                    .setCustomer(stripeCustomerId)
                    .setSubscription(currentSubscription.getId())
                    .addSubscriptionItem(
                            InvoiceUpcomingParams.SubscriptionItem.builder()
                                    .setId(currentSubscription.getItems().getData().get(0).getId())
                                    .setPrice(newPrice.getId()) // Switch to new price
                                    .setQuantity(1L)
                                    .build())
                    .setSubscriptionProrationDate(culSubscriptionProrationDate(currentSubscription));
            if ("trialing".equals(currentSubscription.getStatus())) {
                upcommingParamsBuilder.setSubscriptionTrialEnd(InvoiceUpcomingParams.SubscriptionTrialEnd.NOW);
            }

            Invoice invoice = Invoice.upcoming(upcommingParamsBuilder.build());

            long prorationTotal = 0;
            // 订阅周期相同
            if (Objects.equals(curPrice.getRecurring().getInterval(), newPrice.getRecurring().getInterval())) {
                for (InvoiceLineItem line : invoice.getLines().getData()) {
                    if ("trialing".equals(currentSubscription.getStatus())) {
                        prorationTotal += line.getAmount();
                    } else if (line.getProration()) { // 只提取按比例调整的行项目
                        prorationTotal += line.getAmount();
                    }
                }
            } else {
                for (InvoiceLineItem line : invoice.getLines().getData()) {
                    prorationTotal += line.getAmount();
                    long totalDiscount = line.getDiscountAmounts().stream().mapToLong(InvoiceLineItem.DiscountAmount::getAmount).sum();
                    prorationTotal -= totalDiscount;
                }
            }
            resultDTO.setImmediateCharge(prorationTotal);



        } else {
            resultDTO.setPayType("periodEnd");
        }


        resultDTO.setNextAmount(culculateDiscountPrice(resultDTO.getNextAmount(), currentSubscription));
        resultDTO.setCurrentAmount(culculateDiscountPrice(resultDTO.getCurrentAmount(), currentSubscription));
        return resultDTO;
    }

    @NotNull
    private static Long culculateDiscountPrice(Long amount, Subscription currentSubscription) {
        BigDecimal discountAmountBigDecimal = BigDecimal.valueOf(amount);
        for (Discount discount : CollectionUtils.emptyIfNull(currentSubscription.getDiscountObjects())) {
            BigDecimal percentOff = discount.getCoupon().getPercentOff();
            if (percentOff != null) {
                BigDecimal discountAmount = discountAmountBigDecimal.multiply(percentOff).divide(BigDecimal.valueOf(100), RoundingMode.HALF_DOWN);
                discountAmountBigDecimal = discountAmountBigDecimal.subtract(discountAmount);
            }
            Long amountOff = discount.getCoupon().getAmountOff();
            if (amountOff != null) {
                discountAmountBigDecimal = discountAmountBigDecimal.subtract(BigDecimal.valueOf(amountOff));
            }

        }
        return discountAmountBigDecimal.setScale(0, RoundingMode.HALF_DOWN).longValue();
    }


    private static long culSubscriptionProrationDate(Subscription currentSubscription) {
        long subscriptionProrationDate = System.currentTimeMillis() / 1000;
        subscriptionProrationDate = Math.max(subscriptionProrationDate, currentSubscription.getCurrentPeriodStart());
        return subscriptionProrationDate;
    }

    @Override
    public String getAppCode() {
        return AppEnum.TEXT2GO.getCode();
    }

    public Boolean cancel(Long userId) {
        return Boolean.TRUE;
    }
}
